Vous êtes un expert en extraction d’entités nommées et en désambiguïsation avec Wikidata.
Votre tâche est de :

1. Identifier toutes les entités clés présentes dans le texte fourni. Ne supprime pas les entités n'ayant pas d'identifiant Wikidata.

2. Pour chaque entité, récupérer :
   - son identifiant Wikidata (Qxxx),
   - son label,
   - sa description,
   - son type
   - le nombre de sitelinks (liens vers les versions linguistiques).

3. Calculer un score d’importance pour chaque entité selon la règle suivante :

   a) Fréquence d’apparition (F) :
      Chaque occurrence de l’entité dans le texte vaut 1 point.

   b) Popularité (P) :
      - Récupérer le nombre de sitelinks `S`.
      - Déterminer le maximum `S_max` parmi toutes les entités extraites.
      - Calculer `P = (S / S_max) * 100`.

   c) Bonus pour la présence d’ID Wikidata :
      Si une entrée Wikidata est trouvée pour une entité, appliquer un bonus fixe de +50 points (pour favoriser les entités mappées).
      Ce bonus s’ajoute à `F` mais ne remplace pas la partie `P`.

   d) Score final :
      `importance_score = F + P + bonus` (arrondi à l’entier le plus proche).

4. Retourner un JSON structuré, sans texte supplémentaire :

```json
{
  "entities": [
    {
      "name": "...",
      "wikidata_id": "...",
      "link": "...",
      "description": "...",
      "type": "...",
      "importance_score": ...
    },
    …
  ]
}
```

Assurez‑vous d’utiliser uniquement l’outil `search_wikidata_term` pour chaque terme.
Ne fournissez aucune explication supplémentaire en dehors du JSON demandé.

Texte :

<div><title><p>Génération gratuite d’images avec OpenRouter et Nano-Banana</p></title><body itemscope="" itemtype="https://schema.org/Blog"><p>Aller au contenu</p><div id="page"><div id="content"><div id="primary"><main id="main"><article id="post-6076" itemscope="" itemtype="https://schema.org/CreativeWork"><div><header><h1 itemprop="headline">Génération gratuite d’images
avec OpenRouter et Nano-Banana</h1><div>3 septembre 2025 par ConceptEure</div></header><div itemprop="text"><p>Vous êtes développeur Python en Intelligence Artificielle (IA) et vous cherchez un moyen rapide et gratuit de tester la génération d’images avec une IA ?</p><p>Bonne nouvelle : OpenRouter propose désormais l’accès gratuit à Google Gemini Nano-Banana, un modèle puissant et facile à intégrer dans vos projets.</p><h2>Qu’est-ce que Google Gemini Nano Banana ?</h2><p>Gemini 2.5 Flash Image, surnommé Nano-Banana, est un modèle de génération et d’édition d’images développé par Google.</p><p>Il se distingue par :</p><ul><li>Préservation de l’identité visuelle : l’image conserve son style et ses détails même après plusieurs modifications (coiffure, arrière-plan, accessoires, etc.).</li><li>Fusion texte + image fluide : création de scènes complexes à partir de prompts et d’images de référence.</li><li>Prix compétitif : environ
0,04 € par image (1024×1024) via l’API, avec un accès gratuit via OpenRouter.</li></ul><p>Idéal pour les développeurs qui veulent retoucher des photos, tester des styles créatifs ou intégrer un moteur d’images IA dans leurs applications.</p><h2>Pourquoi utiliser OpenRouter pour Nano-Banana ?</h2><p>OpenRouter agit comme une passerelle API vers plusieurs modèles IA (texte, image, multimodal). Ses avantages principaux :</p><ol><li>Accès gratuit à certains modèles IA, dont Nano-Banana, même sans GPU puissant.</li><li>Routage automatique : si un serveur est indisponible, la requête est redirigée vers un endpoint secondaire.</li><li>Inscription simple : création de compte via e-mail ou Google et génération d’une clé API gratuite (à sauvegarder !).</li><li>Quotas gratuits généreux : jusqu’à 50 requêtes/jour (20/minute). Avec 10 crédits achetés, vous passez à 1000 requêtes/jour.</li></ol><p>👉 En savoir plus sur OpenRouter</p><h2>Tutoriel Python : générer une image avec Nano-Banana</h2><p>Étape 1 : Créer un compte OpenRouter</p><p>Inscrivez-vous gratuitement et récupérez votre clé API.</p><p>Étape 2 : Installer les dépendances</p><pre>pip install Pillow requests</pre><p>Pillow est nécessaire pour manipuler les images, et requests servant à envoyer le prompt à l’endpoint API d’OpenRouter pour utiliser le modèle Nano-Banana (gemini-2.5-flash-image-preview:free).</p><p>Étape 3 : Code Python pour interroger l’API</p><p>Voici le code Python complet d’interrogation de l’API et de conversion du résultat Base64 en une image :</p><div><table><tbody><tr><td><pre>1 2 3 4 5 6 7 8 9 10 11 12 13 14 15 16 17 18 19 20 21 22 23 24 25 26 27 28 29 30 31 32</pre></td><td><pre>import base64, io, os, requests from PIL import Image def gen_image(prompt): headers = { "Authorization": f"Bearer {os.getenv('OPENROUTER_API_KEY')}", "Content-Type": "application/json", } payload = { "model": "google/gemini-2.5-flash-image-preview:free", "messages": [{"role": "user", "content": prompt}], "modalities": ["image", "text"], } response = requests.post("https://openrouter.ai/api/v1/chat/completions", headers=headers, json=payload, timeout=15) result = response.json() if result.get("choices"): images = result["choices"][0]["message"].get("images", []) if images: return images[0]["image_url"]["url"] # Base64 return None def save_image(b64_str, filename="output.png"): data = b64_str.split(",")[1] if b64_str.startswith("data:image") else b64_str img = Image.open(io.BytesIO(base64.b64decode(data))) img.save(filename) print(f"Image enregistrée : {filename}") if __name__ == "__main__": prompt = "Un chaton cyberpunk avec néons violets et arrière-plan futuriste" img_str = gen_image(prompt) if img_str: save_image(img_str)</pre></td></tr></tbody></table></div><p>Étape 4 : Résultat</p><p>Nano-Banana renvoi une chaîne encodée en Base64. Cela signifie que l’image est renvoyée directement dans la réponse API sous forme d’une longue chaîne de caractères codée, pas un lien cliquable vers un fichier image.</p><p>Cette approche est intentionnelle : elle empêche les dépendances à des URLs externes et garantit que l’image est directement encapsulée.</p><p>Il faut donc décoder cette chaîne de caractères et sauvegarder le résultat dans un fichier au format PNG.</p><p>Pourquoi cette combinaison est intéressante ?</p><ul><li>Gratuite : possibilité de tester sans frais.</li><li>Robuste : OpenRouter garantit la disponibilité grâce au routage automatique.</li><li>Qualité : Nano-Banana génère des images cohérentes et visuellement stables. En effet, le modèle Nano-Banana préserve l’identité visuelle des sujets en se souvenant de l’apparence de l’image originale. Il ne se contente pas de jeter les dés en espérant le meilleur. Peu de modèle IA de génération d’image
arrive à faire cela.</li><li>Accessible : intégration simple dans un projet Python avec quelques lignes de code.</li></ul><p>👉 Vous pouvez combiner cette approche avec d’autres modèles via OpenRouter pour enrichir vos workflows IA, voir la documentation officielle OpenRouter.</p><h2>Conclusion</h2><p>L’association OpenRouter + Google Gemini Nano-Banana est une opportunité
unique pour les développeurs IA qui veulent explorer la génération d’images gratuite.</p><p>Entre simplicité d’intégration, quotas gratuits et qualité visuelle, cette solution est parfaite pour vos projets Python d’expérimentation et de prototypage.</p></div><footer aria-label="Méta de l’entrée">Catégories Intelligence artificielle, Python</footer></div></article><div><div
id="comments"><div id="respond"><h3 id="reply-title">Laisser un commentaire Annuler la réponse</h3></div></div></div></main></div><div id="right-sidebar"><div><aside id="block-3"><div><div><ul><li>Monter automatiquement un volume chiffré avec LUKS</li><li>Activer le TPM (fTPM, PTT, PCH-FW) pour installer Windows 11</li><li>Génération gratuite d’images avec OpenRouter et Nano-Banana</li><li>Serveur MCP LitServe – Connectez vos modèles IA à Claude Desktop</li><li>Créer une application Azure pour utiliser l’API Microsoft Graph</li></ul></div></div></aside><aside id="block-9"><ul><li>Astuce</li><li>Fichier 3D STL</li><li>Hardware</li><li>Imprimante 3D</li><li>Intelligence artificielle</li><li>JavaScript</li><li>Jeux vidéo</li><li>Linux</li><li>macOS</li><li>NodeJS</li><li>Outils</li><li>PHP</li><li>Python</li><li>Shell BASH</li><li>Windows</li><li>WordPress</li></ul></aside><aside id="block-4"><div><div><ol><li><article><footer>Lucie sur Afficher la fréquence du CPU et la température dans htop</footer></article></li><li><article><footer>toroidalnaia sur L’écran se verrouille après une minute sur Windows</footer></article></li><li><article><footer>Bebert sur Firefox Snap : comment faire la mise à jour du navigateur sur Ubuntu</footer></article></li><li><article><footer>Bonaventure Claude sur Filament PLA vs PETG : quelle différence ? Qui est le meilleur ?</footer></article></li><li><article><footer>Mathieu sur Ajouter les bonnes permissions sur les fichiers et dossiers de WordPress</footer></article></li></ol></div></div></aside><aside id="block-13"><ul><li>Contact</li><li>Mentions légales</li></ul></aside></div></div></div></div><div><footer aria-label="Site" itemscope="" itemtype="https://schema.org/WPFooter"><div><div>© 2025 ZoneTuto • Construit avec GeneratePress</div></div></footer></div></body></div>
