#!/usr/bin/env python3
"""
Programme d'extraction d'entités nommées avec récupération des QID Wikidata
utilisant smolagents
"""

import json
from typing import Dict, Any, List
import time

import requests

# https://huggingface.co/docs/smolagents/index
# https://github.com/huggingface/smolagents
from smolagents import tool, ToolCallingAgent, OpenAIServerModel, CodeAgent
from smolagents.monitoring import LogLevel
from smolagents.tools import Tool


class WikidataSearchTool(Tool):
    """Outil pour rechercher des entités sur Wikidata"""

    name = "wikidata_search"
    description = "Recherche une entité sur Wikidata et retourne son QID"
    inputs = {
        "entity_name": {
            "type": "string",
            "description": "Nom de l'entité à rechercher sur Wikidata",
        }
    }
    output_type = "string"

    def forward(self, entity_name: str) -> str:
        """Recherche une entité sur Wikidata"""
        try:
            search_url = "https://www.wikidata.org/w/api.php"
            params = {
                "action": "wbsearchentities",
                "format": "json",
                "language": "fr",
                "type": "item",
                "search": entity_name,
                "limit": 1,
            }

            response = requests.get(search_url, params=params, timeout=10)
            response.raise_for_status()

            data = response.json()

            if data.get("search") and len(data["search"]) > 0:
                return data["search"][0]["id"]
            else:
                return "NOT_FOUND"
        except Exception as e:
            print(f"Erreur lors de la recherche Wikidata pour '{entity_name}': {e}")
            return "ERROR"


def create_entity_extractor():
    """Crée l'agent d'extraction d'entités"""

    lmstudio_model = OpenAIServerModel(
        model_id="local-model",  # This can be any name, LM Studio will use whatever model you have loaded
        api_base="http://localhost:1234/v1",
        api_key="not-needed",
        temperature=0.1,
    )

    wikidata_tool = WikidataSearchTool()

    agent = CodeAgent(tools=[wikidata_tool], model=lmstudio_model, max_=10)

    return agent


def extract_entities_with_wikidata(text: str) -> Dict[str, Any]:
    """
    Extrait les entités nommées d'un texte et récupère leurs QID Wikidata
    """

    # Création de l'agent
    agent = create_entity_extractor()

    # Prompt pour l'extraction d'entités
    prompt = f"""
Vous êtes un expert en extraction d'entités nommées.
Votre tâche est d'extraire les entités nommées du texte suivant et de récupérer leur QID Wikidata.

Étapes à suivre :
1. Identifiez toutes les entités nommées dans le texte (personnes, lieux, organisations, concepts, etc.)
2. Pour chaque entité identifiée, utilisez l'outil wikidata_search pour récupérer son QID
3. Retournez un JSON structuré avec les entités et leurs QID

Texte à analyser :
{text}

Format de sortie attendu :
{{
  "entities": [
    {{
      "name": "nom_de_l_entité",
      "wikidata_id": "QID_ou_NOT_FOUND"
    }}
  ]
}}

Commencez l'analyse maintenant.
"""

    result = agent.run(prompt)

    if isinstance(result, str):
        start_idx = result.find("{")
        end_idx = result.rfind("}") + 1

        if start_idx != -1 and end_idx != 0:
            json_str = result[start_idx:end_idx]
            try:
                return json.loads(json_str)
            except json.JSONDecodeError:
                pass
    return {"entities": []}


if __name__ == "__main__":
    text = "La tour Eiffel est située à Paris et a été construite par Gustave Eiffel."
    entities = extract_entities_with_wikidata(text)
    print(entities)
