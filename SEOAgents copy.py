import socket
import json
from datetime import datetime
from typing import List, Dict, Any, Union

# https://huggingface.co/docs/smolagents/index
# https://github.com/huggingface/smolagents
from smolagents import tool, ToolCallingAgent, OpenAIServerModel, CodeAgent
from smolagents.monitoring import LogLevel

# https://github.com/derenrich/wikibase-rest-api-client
from wikibase_rest_api_client import Client
from wikibase_rest_api_client.api.search import search_item
from wikibase_rest_api_client.api.sitelinks import get_item_sitelinks
from wikibase_rest_api_client.types import Response
from wikibase_rest_api_client.models import Error, SearchItemResults, ItemSitelinks

# https://github.com/RDFLib/sparqlwrapper/
from SPARQLWrapper import SPAR<PERSON>Wrapper, JSON


wdclient = Client(
    headers={
        "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36"
    },  # type: ignore
    follow_redirects=True,  # type: ignore
)


def search_wikidata_sitelinks(wikidata_id : str):
    """Search for a wikidata id and return the sitelinks.

    Args:
        wikidata_id: The wikidata id to search for (required)

    Returns:
        A dictionary with the label, aliases and description

    Examples:
        ✅ search_with_wikidata_id("Q2985103")
    """
    response_sl: Response[Union[ItemSitelinks, Error]] = (
        get_item_sitelinks.sync_detailed(wikidata_id, client=wdclient)
    )
    if response_sl.parsed is not None and not isinstance(response_sl.parsed, Error):
        return response_sl.parsed.to_dict()
    return []


def search_wikidata_types(qid_param: str):
    """
    Returns the types (instance of /P31) of a Wikidata QID.
    On Wikidata, each item (Qxxx) is linked to one or more types/classes via the P31 property — "instance of".
    👉 So for a given QID, P31 indicates the instance type (example: Q95 (Google) is an instance of Corporation Q891723).

    Args:
        qid_param: The wikidata id to search for (required)

    Returns:
        A list of types with qid and label

    Examples:
        ✅ get_wikidata_types("Q2985103")
    """

    sparql = SPARQLWrapper(
        "https://query.wikidata.org/sparql",
        agent="Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36"
    )
    sparql.setQuery(f"""
    SELECT ?type ?typeLabel WHERE {{
        wd:{qid_param} wdt:P31 ?type .
        SERVICE wikibase:label {{ bd:serviceParam wikibase:language "fr,en". }}
    }}
    """)
    sparql.setReturnFormat(JSON)

    results_types = sparql.query().convert()
    type_list = []
    for r in results_types["results"]["bindings"]:  # type: ignore
        type_list.append(
            {
                "qid": r["type"]["value"].split("/")[-1],  # type: ignore
                "label": r["typeLabel"]["value"],  # type: ignore
            }
        )
    return type_list


@tool
def search_wikidata_term(search_term: str, lang: str = "fr") -> str: #List[Dict[str, Any]]:
    """Search for a term in wikidata and return the id, label and description in the specified language.

    Args:
        search_term: The term to search for (required)
        lang: The language to return the label and description in (default: fr). It's a 2-letter ISO 3166-1 alpha-2 code
                Examples: 'FR','US', 'UK', 'GB'

    Returns:
        A list of matches with id, label and description

    Examples:
        ✅ search_wikidata_term("Paris")
        ✅ search_wikidata_term("Paris", "fr")
    """
    response_si: Response[Union[SearchItemResults, Error]] = search_item.sync_detailed(
        q=search_term, language=lang, client=wdclient
    )

    matches = []
    if response_si.parsed is not None and not isinstance(response_si.parsed, Error):
        for r in getattr(response_si.parsed, "results", []):
            if r.display_label.value.lower() == search_term.lower():
                #return f"Wikidata QID found for {search_term}. It's equal to {r.id}"
                return f"""
For {search_term}, found this Wikidata information:
```json
{{
  "id": "{r.id}",
  "label": "{r.display_label.value}",
  "description": "{r.description.value}"
}}
```
                """

                sitelinks = len(search_wikidata_sitelinks(r.id))
                entity_types = []
                for entity_type in search_wikidata_types(r.id):
                    entity_types.append(
                        {"qid": entity_type["qid"], "label": entity_type["label"]}
                    )
                matches.append(
                    {
                        "id": r.id,
                        "label": r.display_label.value,
                        "description": r.description.value,
                        "types": entity_types,
                        "sitelinks": sitelinks,
                    }
                )

    return f"For {search_term}, I found no Wikidata information."

    """else:
        if response_si.parsed is not None:
            return [
                {
                    "error": getattr(response_si.parsed, "error", "Unknown error"),
                    "details": getattr(
                        response_si.parsed, "message", "No details provided"
                    ),
                }
            ]
        else:
            return [
                {
                    "error": "Unknown error",
                    "details": "No details provided",
                }
            ]

    if len(matches) == 0:
        return json.dumps([{"error": "No results found", "details": "No details provided"}])

    return json.dumps(matches)"""


if __name__ == "__main__":
    lmstudio_model = OpenAIServerModel(
        model_id="local-model",  # This can be any name, LM Studio will use whatever model you have loaded
        api_base="http://localhost:1234/v1",
        api_key="not-needed",
        temperature=0.1,
    )

    agents_manager = CodeAgent(
        tools=[
            search_wikidata_term,
        ],
        model=lmstudio_model,
        max_steps=20,
        stream_outputs=True,
        verbosity_level=LogLevel.INFO,
    )


    task_prompt = """
Vous êtes un expert en extraction d’entités nommées.
Votre tâche est d'extraire les entités nommées d'un texte.
Pour chaque entité extraite, récupérer QID Wikidata
Retourner un JSON structuré, sans texte supplémentaire, sous la forme suivante :

```json
{
  "entities": [
    {
      "name": "...",         # Nom de l'entité extraite
      "wikidata_id": "...",  # QID Wikidata de l'entité extraite
    },
    …
  ]
}
```

Texte :

<div><title><p>Génération gratuite d’images avec OpenRouter et Nano-Banana</p></title><body itemscope="" itemtype="https://schema.org/Blog"><p>Aller au contenu</p><div id="page"><div id="content"><div id="primary"><main id="main"><article id="post-6076" itemscope="" itemtype="https://schema.org/CreativeWork"><div><header><h1 itemprop="headline">Génération gratuite d’images
avec OpenRouter et Nano-Banana</h1><div>3 septembre 2025 par ConceptEure</div></header><div itemprop="text"><p>Vous êtes développeur Python en Intelligence Artificielle (IA) et vous cherchez un moyen rapide et gratuit de tester la génération d’images avec une IA ?</p><p>Bonne nouvelle : OpenRouter propose désormais l’accès gratuit à Google Gemini Nano-Banana, un modèle puissant et facile à intégrer dans vos projets.</p><h2>Qu’est-ce que Google Gemini Nano Banana ?</h2><p>Gemini 2.5 Flash Image, surnommé Nano-Banana, est un modèle de génération et d’édition d’images développé par Google.</p><p>Il se distingue par :</p><ul><li>Préservation de l’identité visuelle : l’image conserve son style et ses détails même après plusieurs modifications (coiffure, arrière-plan, accessoires, etc.).</li><li>Fusion texte + image fluide : création de scènes complexes à partir de prompts et d’images de référence.</li><li>Prix compétitif : environ
0,04 € par image (1024×1024) via l’API, avec un accès gratuit via OpenRouter.</li></ul><p>Idéal pour les développeurs qui veulent retoucher des photos, tester des styles créatifs ou intégrer un moteur d’images IA dans leurs applications.</p><h2>Pourquoi utiliser OpenRouter pour Nano-Banana ?</h2><p>OpenRouter agit comme une passerelle API vers plusieurs modèles IA (texte, image, multimodal). Ses avantages principaux :</p><ol><li>Accès gratuit à certains modèles IA, dont Nano-Banana, même sans GPU puissant.</li><li>Routage automatique : si un serveur est indisponible, la requête est redirigée vers un endpoint secondaire.</li><li>Inscription simple : création de compte via e-mail ou Google et génération d’une clé API gratuite (à sauvegarder !).</li><li>Quotas gratuits généreux : jusqu’à 50 requêtes/jour (20/minute). Avec 10 crédits achetés, vous passez à 1000 requêtes/jour.</li></ol><p>👉 En savoir plus sur OpenRouter</p><h2>Tutoriel Python : générer une image avec Nano-Banana</h2><p>Étape 1 : Créer un compte OpenRouter</p><p>Inscrivez-vous gratuitement et récupérez votre clé API.</p><p>Étape 2 : Installer les dépendances</p><pre>pip install Pillow requests</pre><p>Pillow est nécessaire pour manipuler les images, et requests servant à envoyer le prompt à l’endpoint API d’OpenRouter pour utiliser le modèle Nano-Banana (gemini-2.5-flash-image-preview:free).</p><p>Étape 3 : Code Python pour interroger l’API</p><p>Voici le code Python complet d’interrogation de l’API et de conversion du résultat Base64 en une image :</p><div><table><tbody><tr><td><pre>1 2 3 4 5 6 7 8 9 10 11 12 13 14 15 16 17 18 19 20 21 22 23 24 25 26 27 28 29 30 31 32</pre></td><td><pre>import base64, io, os, requests from PIL import Image def gen_image(prompt): headers = { "Authorization": f"Bearer {os.getenv('OPENROUTER_API_KEY')}", "Content-Type": "application/json", } payload = { "model": "google/gemini-2.5-flash-image-preview:free", "messages": [{"role": "user", "content": prompt}], "modalities": ["image", "text"], } response = requests.post("https://openrouter.ai/api/v1/chat/completions", headers=headers, json=payload, timeout=15) result = response.json() if result.get("choices"): images = result["choices"][0]["message"].get("images", []) if images: return images[0]["image_url"]["url"] # Base64 return None def save_image(b64_str, filename="output.png"): data = b64_str.split(",")[1] if b64_str.startswith("data:image") else b64_str img = Image.open(io.BytesIO(base64.b64decode(data))) img.save(filename) print(f"Image enregistrée : {filename}") if __name__ == "__main__": prompt = "Un chaton cyberpunk avec néons violets et arrière-plan futuriste" img_str = gen_image(prompt) if img_str: save_image(img_str)</pre></td></tr></tbody></table></div><p>Étape 4 : Résultat</p><p>Nano-Banana renvoi une chaîne encodée en Base64. Cela signifie que l’image est renvoyée directement dans la réponse API sous forme d’une longue chaîne de caractères codée, pas un lien cliquable vers un fichier image.</p><p>Cette approche est intentionnelle : elle empêche les dépendances à des URLs externes et garantit que l’image est directement encapsulée.</p><p>Il faut donc décoder cette chaîne de caractères et sauvegarder le résultat dans un fichier au format PNG.</p><p>Pourquoi cette combinaison est intéressante ?</p><ul><li>Gratuite : possibilité de tester sans frais.</li><li>Robuste : OpenRouter garantit la disponibilité grâce au routage automatique.</li><li>Qualité : Nano-Banana génère des images cohérentes et visuellement stables. En effet, le modèle Nano-Banana préserve l’identité visuelle des sujets en se souvenant de l’apparence de l’image originale. Il ne se contente pas de jeter les dés en espérant le meilleur. Peu de modèle IA de génération d’image
arrive à faire cela.</li><li>Accessible : intégration simple dans un projet Python avec quelques lignes de code.</li></ul><p>👉 Vous pouvez combiner cette approche avec d’autres modèles via OpenRouter pour enrichir vos workflows IA, voir la documentation officielle OpenRouter.</p><h2>Conclusion</h2><p>L’association OpenRouter + Google Gemini Nano-Banana est une opportunité
unique pour les développeurs IA qui veulent explorer la génération d’images gratuite.</p><p>Entre simplicité d’intégration, quotas gratuits et qualité visuelle, cette solution est parfaite pour vos projets Python d’expérimentation et de prototypage.</p></div><footer aria-label="Méta de l’entrée">Catégories Intelligence artificielle, Python</footer></div></article><div><div
id="comments"><div id="respond"><h3 id="reply-title">Laisser un commentaire Annuler la réponse</h3></div></div></div></main></div><div id="right-sidebar"><div><aside id="block-3"><div><div><ul><li>Monter automatiquement un volume chiffré avec LUKS</li><li>Activer le TPM (fTPM, PTT, PCH-FW) pour installer Windows 11</li><li>Génération gratuite d’images avec OpenRouter et Nano-Banana</li><li>Serveur MCP LitServe – Connectez vos modèles IA à Claude Desktop</li><li>Créer une application Azure pour utiliser l’API Microsoft Graph</li></ul></div></div></aside><aside id="block-9"><ul><li>Astuce</li><li>Fichier 3D STL</li><li>Hardware</li><li>Imprimante 3D</li><li>Intelligence artificielle</li><li>JavaScript</li><li>Jeux vidéo</li><li>Linux</li><li>macOS</li><li>NodeJS</li><li>Outils</li><li>PHP</li><li>Python</li><li>Shell BASH</li><li>Windows</li><li>WordPress</li></ul></aside><aside id="block-4"><div><div><ol><li><article><footer>Lucie sur Afficher la fréquence du CPU et la température dans htop</footer></article></li><li><article><footer>toroidalnaia sur L’écran se verrouille après une minute sur Windows</footer></article></li><li><article><footer>Bebert sur Firefox Snap : comment faire la mise à jour du navigateur sur Ubuntu</footer></article></li><li><article><footer>Bonaventure Claude sur Filament PLA vs PETG : quelle différence ? Qui est le meilleur ?</footer></article></li><li><article><footer>Mathieu sur Ajouter les bonnes permissions sur les fichiers et dossiers de WordPress</footer></article></li></ol></div></div></aside><aside id="block-13"><ul><li>Contact</li><li>Mentions légales</li></ul></aside></div></div></div></div><div><footer aria-label="Site" itemscope="" itemtype="https://schema.org/WPFooter"><div><div>© 2025 ZoneTuto • Construit avec GeneratePress</div></div></footer></div></body></div>
"""


    result = agents_manager.run(
        task=task_prompt,
        max_steps=10,
    )

    print(result)
